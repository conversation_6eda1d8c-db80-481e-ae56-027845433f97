"use client";

import { useUser } from "@clerk/nextjs";
import { Button, Dropdown, DropdownItem, DropdownMenu, DropdownTrigger, Tooltip } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { trpc } from "@/app/_trpc/client";
import { useUpgradeModal } from "@/contexts/upgrade-modal-context";
import { CreateResumeModal } from "./create-resume-modal";

interface CreateResumeButtonProps {
  size?: "sm" | "md" | "lg";
  text?: string;
  className?: string;
}

export default function CreateResumeButton({
  size = "lg",
  text = "Create Resume",
  className = "",
}: CreateResumeButtonProps) {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const { showUpgradeModal } = useUpgradeModal();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { data: plan, isLoading: isPlanLoading } = trpc.user.getPlan.useQuery();

  const handleCreateNew = () => {
    if (!isLoaded || isPlanLoading) return;

    if (!user) {
      router.push("/sign-in");
      return;
    }

    if (plan?.isFree && plan?.resumeCount >= 1) {
      showUpgradeModal();
      return;
    }

    // Open the create resume modal
    setIsModalOpen(true);
  };

  const handleImportLinkedIn = () => {
    if (!isLoaded || isPlanLoading) return;

    if (!user) {
      router.push("/sign-in");
      return;
    }

    if (plan?.isFree && plan?.resumeCount >= 1) {
      showUpgradeModal();
      return;
    }

    // For now, also open the create modal for LinkedIn import
    // TODO: Implement LinkedIn import functionality
    setIsModalOpen(true);
  };

  const isButtonDisabled = !isLoaded || isPlanLoading;

  const button = (
    <Button
      className={className}
      color="primary"
      radius="full"
      size={size}
      variant="shadow"
      endContent={<Icon icon="lucide:chevron-down" className="w-4 h-4" />}
      isLoading={!isLoaded || isPlanLoading}
      isDisabled={isButtonDisabled}
    >
      {text || "Get Started"}
    </Button>
  );

  // Ensure we have a valid element for Tooltip/Dropdown
  const dropdownTrigger = plan?.isFree && plan?.resumeCount >= 1 ? (
    <Tooltip content="Upgrade to create more resumes" placement="top">
      <span>{button}</span>
    </Tooltip>
  ) : (
    button
  );

  return (
    <>
      <Dropdown>
        <DropdownTrigger>
          {dropdownTrigger}
        </DropdownTrigger>
        <DropdownMenu aria-label="Resume creation options">
          <DropdownItem key="create" onClick={handleCreateNew}>
            <div className="flex items-center gap-2">
              <Icon icon="lucide:file-plus" className="w-4 h-4" />
              Create New Resume
            </div>
          </DropdownItem>
          <DropdownItem key="linkedin" onClick={handleImportLinkedIn}>
            <div className="flex items-center gap-2">
              <Icon icon="lucide:linkedin" className="w-4 h-4" />
              Import from LinkedIn
            </div>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>

      <CreateResumeModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
}
